import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/etablissement_entity.dart'; // Assuming an entity for Etablissement
import '../entities/etablissement_utilisateur.dart'; // Import EtablissementUtilisateur

/// Abstract repository interface for schools operations
abstract class SchoolsRepository {
  /// Get list of all available schools
  Future<Either<Failure, List<EtablissementEntity>>> getAllAvailableSchools();

  /// Get list of schools associated with the current user
  Future<Either<Failure, List<EtablissementUtilisateur>>> getUserSchools(String phoneNumber);

}