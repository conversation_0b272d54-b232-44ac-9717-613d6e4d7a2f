﻿import 'package:equatable/equatable.dart';

/// Base profile state
abstract class ProfileState extends Equatable {
  const ProfileState();
  
  @override
  List<Object?> get props => [];
}

/// Initial profile state
class ProfileInitial extends ProfileState {
  const ProfileInitial();
}

/// Loading state during profile operations
class ProfileLoading extends ProfileState {
  const ProfileLoading();
}

/// Profile data loaded successfully
class ProfileLoaded extends ProfileState {
  final List<dynamic> data; // TODO: Replace with proper type
  
  const ProfileLoaded({required this.data});
  
  @override
  List<Object?> get props => [data];
}

/// Profile error occurred
class ProfileError extends ProfileState {
  final String message;
  
  const ProfileError(this.message);
  
  @override
  List<Object?> get props => [message];
}
