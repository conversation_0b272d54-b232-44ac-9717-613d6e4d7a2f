import 'package:Kairos/core/widgets/common/snackbar_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:Kairos/core/di/injection_container.dart';
import 'package:Kairos/features/authentication/presentation/bloc/cubit/code_activation_cubit.dart'; // Correct import
import 'package:Kairos/features/authentication/presentation/bloc/state/auth_state.dart'; // Correct import
import 'package:Kairos/features/authentication/presentation/bloc/cubit/phone_authentication_cubit.dart'; // Correct import
import 'code_activation_error_dialog.widget.dart';
import 'package:flutter/gestures.dart';

class CodeActivationWidget extends StatefulWidget {
  const CodeActivationWidget({super.key, required this.pageController, required this.phoneNumber});
  final PageController pageController;
  final String phoneNumber;

  @override
  State<CodeActivationWidget> createState() => _CodeActivationWidgetState();
}

class _CodeActivationWidgetState extends State<CodeActivationWidget> {
  final TextEditingController _codeController = TextEditingController();
  final TextEditingController _fullnameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  void dispose() {
    _codeController.dispose();
    _fullnameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<PhoneAuthenticationCubit, AuthState>(
      listener: (phoneAuthContext, state) {
        if (state is AuthSmsSent) {
          // Show success message
          setState(() {
            _isLoading = false;
          });
          ScaffoldMessenger.of(phoneAuthContext).showSnackBar(
            CustomSnackbar(
              message: "Code d'activation renvoyé au numéro: ${widget.phoneNumber}",
            ).getSnackBar(),
          );
        } else if (state is AuthSmsSending) {
           setState(() {
            _isLoading = true;
          });
        } else if (state is AuthSmsError) {
           setState(() {
            _isLoading = false;
          });
        }
      },
      child: BlocProvider<CodeActivationCubit>(
        create: (context) => sl<CodeActivationCubit>(),
        child: BlocConsumer<CodeActivationCubit, AuthState>(
          listener: (codeActivationContext, state) {
            if (state is CodeActivationLoadingState) {
              setState(() {
                _isLoading = true;
              });
            } else if (state is CodeActivationSuccessState) {
              setState(() {
                _isLoading = false;
              });
              widget.pageController.nextPage(
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeInOut,
              );
            } else if (state is CodeActivationErrorState) {
              setState(() {
                _isLoading = false;
              });
              showDialog(
                context: codeActivationContext,
                builder: (BuildContext dialogContext) {
                  return CodeActivationErrorDialog(
                    returnCode: state.returnCode,
                    userMessage: state.userMessage,
                    onResend: () {
                      codeActivationContext.read<PhoneAuthenticationCubit>().resendSms(widget.phoneNumber);
                    },
                  );
                },
              );
            }
          },
          builder: (codeActivationContext, state) {
            return Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Text(
                  "CODE D'ACTIVATION",
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(codeActivationContext).primaryColor,
                  ),
                ),
                SvgPicture.asset("assets/images/logo_kairos.svg"),
                Divider(
                  color: Theme.of(codeActivationContext).primaryColor,
                  thickness: 5,
                  height: 20,
                  indent: 100,
                  endIndent: 100,
                ),
                const Spacer(),
                Flexible(flex: 8, child: SvgPicture.asset("assets/images/otp_code.svg")),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.0),
                  child: Text(
                    "Veuillez saisir le code d'activation envoyé à votre numéro de téléphone ainsi que votre nom complet.",
                    textAlign: TextAlign.center,
                  ),
                ),
                const Spacer(flex: 3),
                Form(
                  key: _formKey,
                  child: SizedBox(
                    width: 300,
                    child: Column( // Wrap TextFormFields in a Column
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        TextFormField(
                          controller: _codeController,
                          decoration: InputDecoration(
                            border: OutlineInputBorder(),
                            hintText: "Code d'activation",
                            hintStyle: TextStyle(color: Colors.grey.shade400),
                            contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                          ),
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                          ],
                          validator: (value) {
                            if (value!.isEmpty || value.length < 4) {
                              return "Veuillez saisir le code d'activation";
                            } else {
                              return null;
                            }
                          },
                        ),
                        SizedBox(height: 15), // Add spacing between fields
                        TextFormField(
                          controller: _fullnameController,
                          decoration: InputDecoration(
                            border: OutlineInputBorder(),
                            hintText: "Nom complet",
                            hintStyle: TextStyle(color: Colors.grey.shade400),
                            contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                          ),
                          keyboardType: TextInputType.name,
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(RegExp(r'[^0-9]')),
                          ],
                          validator: (value) {
                            if (value!.isEmpty) {
                              return "Veuillez saisir votre nom complet";
                            } else {
                              return null;
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                const Spacer(),
                FilledButton(
                  style: ButtonStyle(
                    backgroundColor: WidgetStateProperty.all(Theme.of(codeActivationContext).primaryColor),
                    fixedSize: WidgetStateProperty.all(Size(300, 50)),
                  ),
                  onPressed: _isLoading ? null : () {
                    debugPrint('the user clicked on `Continue` button');
                    if (_formKey.currentState!.validate()) {
                      // Call verifyPin method in CodeActivationCubit
                      debugPrint('CodeActivationWidget: Calling verifyPin with fullName: ${_fullnameController.text}, otp: ${_codeController.text}, phoneNumber: ${widget.phoneNumber}');
                      codeActivationContext.read<CodeActivationCubit>().verifyPin(
                        _fullnameController.text,
                        _codeController.text,
                        widget.phoneNumber,
                      );
                    }
                  },
                  child: _isLoading
                      ? SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : Text(
                          "CONTINUER",
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                ),
                const Spacer(),
                Text.rich(
                  TextSpan(
                    children: [
                      const TextSpan(text: "Vous n'avez pas reçu le code d'activation? "),
                      TextSpan(
                        text: "Renvoyer",
                        style: TextStyle(
                          color: Theme.of(codeActivationContext).primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            codeActivationContext.read<PhoneAuthenticationCubit>().resendSms(widget.phoneNumber);
                          },
                      ),
                    ],
                  ),
                ),
                const Spacer(flex: 3),
              ],
            );
          },
        ),
      ),
    );
  }
}
