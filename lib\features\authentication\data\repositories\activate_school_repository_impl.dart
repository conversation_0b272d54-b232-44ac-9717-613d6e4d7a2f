import 'dart:async';

import 'package:Kairos/core/error/exceptions.dart';
import 'package:Kairos/core/error/failures.dart';
import 'package:Kairos/features/authentication/data/datasources/activate_school_remote_datasource.dart';
import 'package:Kairos/features/schools/data/models/activation_request.dart';
import 'package:dartz/dartz.dart';
import 'package:dio/dio.dart';

import '../../domain/repositories/activate_school_repository.dart';

/// Concrete implementation of [ActivateSchoolRepository].
///
/// Provides the actual implementation for school activation operations.
class ActivateSchoolRepositoryImpl implements ActivateSchoolRepository {
  /// Constructs an [ActivateSchoolRepositoryImpl].
  ///
  /// Dependencies like data sources would be injected here in a real scenario.
  ActivateSchoolRepositoryImpl({required this.remoteDataSource});

  final ActivateSchoolRemoteDatasource remoteDataSource;

  @override
  Future<Either<Failure, void>> activateSchool(ActivationRequest request) async {
    try {
      await remoteDataSource.activateSchool(request);
      return const Right(null); // Indicate success
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } on DioException catch (e) {
       // Handle Dio errors (HTTP errors, network issues, etc.)
      return Left(ServerFailure('Failed to activate school: ${e.message}'));
    } catch (e) {
      return Left(UnexpectedFailure(e.toString()));
    }
  }
}