
import 'package:Kairos/core/device_info.dart';

class ActivationRequest extends DeviceInfo {
  final String codeUtilisateur;
  final String codeEtab;
  final String password;
  ActivationRequest({
    required super.numeroTelephone,
    required super.marqueTelephone,
    required super.modelTelephone,
    required super.imeiTelephone,
    required super.numeroSerie,
    required this.codeUtilisateur,
    required this.codeEtab,
    required this.password,
  });

  factory ActivationRequest.fromJson(Map<String, dynamic> json) {
    return ActivationRequest(
      numeroTelephone: json['numeroTelephone'],
      marqueTelephone: json['marqueTelephone'],
      modelTelephone: json['modelTelephone'],
      imeiTelephone: json['imeiTelephone'],
      numeroSerie: json['numeroSerie'],
      codeUtilisateur: json['codeUtilisateur'],
      codeEtab: json['codeEtab'],
      password: json['password'],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'codeUtilisateur': codeUtilisateur,
      'codeEtab': codeEtab,
      'password': password,
    });
    return json;
  }
}
