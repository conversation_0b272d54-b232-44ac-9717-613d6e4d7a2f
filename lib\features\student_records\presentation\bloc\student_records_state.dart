﻿import 'package:equatable/equatable.dart';

/// Base student_records state
abstract class StudentRecordsState extends Equatable {
  const StudentRecordsState();
  
  @override
  List<Object?> get props => [];
}

/// Initial student_records state
class StudentRecordsInitial extends StudentRecordsState {
  const StudentRecordsInitial();
}

/// Loading state during student_records operations
class StudentRecordsLoading extends StudentRecordsState {
  const StudentRecordsLoading();
}

/// StudentRecords data loaded successfully
class StudentRecordsLoaded extends StudentRecordsState {
  final List<dynamic> data; // TODO: Replace with proper type
  
  const StudentRecordsLoaded({required this.data});
  
  @override
  List<Object?> get props => [data];
}

/// StudentRecords error occurred
class StudentRecordsError extends StudentRecordsState {
  final String message;
  
  const StudentRecordsError(this.message);
  
  @override
  List<Object?> get props => [message];
}
