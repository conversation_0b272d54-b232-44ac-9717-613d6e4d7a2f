import 'package:Kairos/core/widgets/common/hero_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_bloc/flutter_bloc.dart'; // Import flutter_bloc
import 'package:Kairos/features/schools/presentation/pages/liste_etablissements/liste_etablissement_widgets/list_schools.widget.dart';
import 'package:Kairos/features/schools/presentation/pages/liste_etablissements/liste_etablissement_widgets/liste_empty_alert.widget.dart';
import 'package:Kairos/core/widgets/indicators/custom_spinner.dart';
import 'package:Kairos/features/schools/presentation/bloc/schools_cubit.dart'; // Import SchoolsCubit
import 'package:Kairos/features/schools/presentation/bloc/schools_state.dart'; // Import SchoolsState
import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart'; // Import AuthLocalDataSource
import 'package:Kairos/core/di/injection_container.dart'; // Import sl
import 'package:Kairos/features/authentication/presentation/pages/activate_school/activate_school.page.dart';

class ListeEtablissement extends StatefulWidget{
  const ListeEtablissement({super.key});

  @override
  State<ListeEtablissement> createState() => _ListeEtablissementState();
}

class _ListeEtablissementState extends State<ListeEtablissement>{
  // Inject AuthLocalDataSource
  final AuthLocalDataSource _authLocalDataSource = sl<AuthLocalDataSource>();

  @override
  void initState() {
    super.initState();
    // Fetch user schools when the page initializes
    _fetchSchools();
  }

  /// Fetches user schools using the SchoolsCubit
  Future<void> _fetchSchools() async {
    // Retrieve the user's phone number from local storage
    final phoneNumber = await _authLocalDataSource.getPhoneNumber();

    // Check if phone number is available and fetch schools
    if (phoneNumber != null) {
      // Use context.read to access the SchoolsCubit
      context.read<SchoolsCubit>().getUserSchools(phoneNumber);
   } else {
     // Handle case where phone number is not available (e.g., show an error or prompt)
      // For now, we can log a message or show a snackbar
      debugPrint('Phone number not found locally.');
      // Optionally, emit an error state to the cubit
      // context.read<SchoolsCubit>().emit(SchoolsError("Phone number not found."));
    }
  }

  @override
  Widget build(BuildContext context){
    return Scaffold(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          HeroWidget(),
          SizedBox(height: 20),
          Text("LISTE DES ETABLISSEMENTS", style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor)),
          // Only show this padding when state is SchoolsLoaded
          BlocBuilder<SchoolsCubit, SchoolsState>(
            builder: (context, state) {
              if (state is SchoolsLoaded) {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20.0),
                  child: Text("Veuillez sélectionner votre établissement.", textAlign: TextAlign.center),
                );
              }
              return SizedBox.shrink();
            },
          ),
          SizedBox(height: 2),
          // Use BlocBuilder to react to SchoolsCubit states
          BlocBuilder<SchoolsCubit, SchoolsState>(
            key: const ValueKey('schools_bloc_builder'), // Added a key
            builder: (context, state) {
              debugPrint('BlocBuilder received state: $state'); // Added debug print
              if (state is SchoolsLoading) {
                // Display loading indicator
                return Flexible(
                  flex: 6,
                  child: Center(
                    child: CustomSpinner(
                      size: 60.0,
                      strokeWidth: 5.0,
                    ),
                  ),
                );
              } else if (state is SchoolsLoaded) {
                // Display the list of schools
                return Flexible(
                  flex: 6,
                  child: ListeEtablissementUtilisateurWidget(userSchools: state.data), // Pass actual data from state
                );
              } else if (state is SchoolsEmpty) {
                // Display empty state message
                debugPrint('Schools are empty');
                return Flexible(
                  flex: 6,
                  child: ListeEmptyAlert(),
                );
              } else if (state is SchoolsError) {
                // Display error message
                return Flexible(
                  flex: 6,
                  child: Center(
                    child: Text("Error: ${state.message}"), // Display error message
                  ),
                );
              }
              // Initial state or any other state
              return Flexible(
                flex: 6,
                child: Container(), // Or a default empty state
              );
            },
          ),
          SizedBox(height: 20),
          FilledButton(
            style: ButtonStyle(
              backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
              fixedSize: WidgetStateProperty.all(Size(300, 50))
            ),
            onPressed: () {
              debugPrint('the user clicked on `Continue` button');
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => ActivateSchoolPage()),
              );
            },
            child: Text("ACTIVER NOUVEL ÉTABLISSEMENT", style: TextStyle(fontWeight: FontWeight.bold), ),
          ),
          SizedBox(height: 20),
          SvgPicture.asset("assets/images/logo_footer.svg"),
          SizedBox(height: 10)
        ],
      ),
    );
  }
}