import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:Kairos/features/authentication/presentation/pages/accueil/accueil_widgets/app_activated.widget.dart';
import 'package:Kairos/features/authentication/presentation/pages/accueil/accueil_widgets/code_activation.widget.dart';
import 'package:Kairos/features/authentication/presentation/pages/accueil/accueil_widgets/phone_authentication.widget.dart';
import 'package:Kairos/features/authentication/presentation/pages/accueil/accueil_widgets/home_slide.widget.dart';
import 'package:Kairos/features/authentication/presentation/bloc/cubit/phone_authentication_cubit.dart';
import 'package:Kairos/features/authentication/presentation/bloc/state/auth_state.dart';
import 'package:Kairos/core/di/injection_container.dart' as di;

class AccueilPage extends StatefulWidget{
  const AccueilPage({super.key});

  @override
  State<AccueilPage> createState() => _AccueilPageState();
}

class _AccueilPageState extends State<AccueilPage>{
  final PageController _pageController = PageController();
  String? _phoneNumber; // Add state variable for phone number

  @override
  Widget build(BuildContext context){
    return Scaffold(
      body:
      BlocProvider<PhoneAuthenticationCubit>( // Provide PhoneAuthenticationCubit here
        create: (context) => di.sl<PhoneAuthenticationCubit>(), // Get cubit from service locator
        child: BlocConsumer<PhoneAuthenticationCubit, AuthState>( // Consume PhoneAuthenticationCubit state
          listener: (context, state) {
            // Update phone number state when SMS is sent or fails
            if (state is AuthSmsSent) {
              setState(() {
                _phoneNumber = state.phoneNumber;
              });
            } else if (state is AuthSmsError) {
               setState(() {
                _phoneNumber = state.phoneNumber;
              });
            }
          },
          builder: (context, state) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Spacer(),
                SizedBox(height: 40),
               Expanded(
                // height: MediaQuery.of(context).size.height * .80,
                child: PageView(
                  physics: const OnlyAllowLeftSwipeScrollPhysics(),
                  controller: _pageController,
                  children: [
                    HomeSlideWidget(pageController: _pageController),
                    PhoneAuthentication(pageController: _pageController), // PhoneAuthentication is now a direct child
                    CodeActivationWidget(pageController: _pageController, phoneNumber: _phoneNumber ?? ''), // Pass phone number
                    AppActivatedWidget(pageController: _pageController),
                  ],
                ),
               ),
               // SizedBox(height: 40),
               // Spacer(flex: 1,),
               SvgPicture.asset("assets/images/logo_footer.svg"),
               SizedBox(height: 10)
              ],
            );
          },
        ),
      ),
    );
  }
}



class OnlyAllowLeftSwipeScrollPhysics extends ScrollPhysics {
  const OnlyAllowLeftSwipeScrollPhysics({ScrollPhysics? parent}) : super(parent: parent);

  @override
  OnlyAllowLeftSwipeScrollPhysics applyTo(ScrollPhysics? ancestor) {
    return OnlyAllowLeftSwipeScrollPhysics(parent: buildParent(ancestor));
  }

  @override
  double applyPhysicsToUserOffset(ScrollMetrics position, double offset) {
    // Only allow negative offset (swiping left)
    if (offset < 0) {
      return offset;
    }
    return 0.0;
  }
}