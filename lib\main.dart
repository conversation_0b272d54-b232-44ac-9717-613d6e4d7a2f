import 'package:Kairos/core/services/device_info_service.dart';
import 'package:Kairos/features/schools/presentation/bloc/schools_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart'; // Import flutter_bloc
import 'package:Kairos/features/authentication/presentation/pages/accueil/accueil.page.dart';
import 'package:Kairos/features/dashboard/presentation/pages/dashboard/dashboard.page.dart';
import 'package:Kairos/features/schools/presentation/pages/liste_etablissements/liste_etablissement.page.dart';
import 'package:Kairos/features/notifications/presentation/pages/notifications/notifications.page.dart';
import 'package:Kairos/features/profile/presentation/pages/profile/profile.page.dart';
import 'package:Kairos/features/splash/presentation/pages/splashscreen.dart';
import 'package:Kairos/features/schools/presentation/pages/dossier_selection/dossier_selection.page.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:Kairos/core/theme/app_theme.dart';
import "package:Kairos/core/di/injection_container.dart" as di;

void main() async{
  WidgetsFlutterBinding.ensureInitialized();
  await di.init();
  await DeviceInfoService.init();
  runApp(const KairosMobileApp());
}


class KairosMobileApp extends StatelessWidget{
  const KairosMobileApp({super.key});

  @override
  Widget build(BuildContext context){
    return BlocProvider<SchoolsCubit>(
      create: (context) => di.sl<SchoolsCubit>(),
      child: MaterialApp(
          debugShowCheckedModeBanner: false,
          title: "Kairos Mobile",
          locale: Locale("fr", "FR"),
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale("fr", "FR"),
          ],
          theme: AppTheme.lightTheme,
          home: const Splashscreen(),
          builder: (context, child){
            if(child is Splashscreen || child is AccueilPage){
              return child!;
            } else {
              return SafeArea(child: child!);
            }
          },
          routes: {
            "/accueil": (context) => const AccueilPage(),
            "/liste_etablissement": (context) => const ListeEtablissement(),
            "/dashboard": (context) {
              final args = ModalRoute.of(context)?.settings.arguments as String?;
              return Dashboard(userName: args);
            },
            "/profile": (context) => const ProfilePage(),
            "/notifications": (context) => NotificationsPage(),
            "/dossier_selection": (context) => const DossierSelectionPage(),
          }

        ),
    );
  }
}
