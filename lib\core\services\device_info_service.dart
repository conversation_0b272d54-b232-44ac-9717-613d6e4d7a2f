import "dart:convert";
import "dart:io";
import "package:Kairos/core/device_info.dart";
import "package:device_info_plus/device_info_plus.dart";
import "package:flutter/material.dart";
import "package:mobile_device_identifier/mobile_device_identifier.dart";
import "package:shared_preferences/shared_preferences.dart";


class DeviceInfoService {

  static final DeviceInfoPlugin _deviceInfoPlugin = DeviceInfoPlugin();
  static final MobileDeviceIdentifier _mobileDeviceIdentifer = MobileDeviceIdentifier();
  static late final DeviceInfo _deviceInfo;
  static DeviceInfo get deviceInfo => _deviceInfo;



    static Future<void> init() async{
      debugPrint("DeviceInfoService: INITIALIZING DEVICE INFO");
      
      try{
        if(await _isDeviceInfoAlreadyStored()){
          _deviceInfo = await loadDeviceInfo();
        } else {
        if(Platform.isAndroid){
          _deviceInfo = await _readAndroidDeviceInfo(await _deviceInfoPlugin.androidInfo);
          saveDeviceInfo(_deviceInfo);
        } else if(Platform.isIOS){
          _deviceInfo = await _readIosDeviceInfo(await _deviceInfoPlugin.iosInfo);
          saveDeviceInfo(_deviceInfo);
        } else {
          throw Exception("Platform not supported");
        }
      }
      }
      catch(e){
        debugPrint("DeviceInfoService: ERROR: $e");
        _deviceInfo = DeviceInfo(
          marqueTelephone: "Unknown",
          modelTelephone: "Unknown",
          imeiTelephone: "Unknown",
          numeroSerie: "Unknown",
          );
      }
    }


static void saveDeviceInfo(DeviceInfo deviceInfo) async{
  final prefs = await SharedPreferences.getInstance();
  debugPrint("DeviceInfoService: SAVING DEVICE INFO: ${deviceInfo.toJson()}");
  prefs.setString("deviceInfo", deviceInfo.toJson().toString());
}



static Future<bool> _isDeviceInfoAlreadyStored() async {
  final prefs = await SharedPreferences.getInstance();
  return prefs.containsKey("deviceInfo");
}

static Future<DeviceInfo> loadDeviceInfo() async{
  final prefs = await SharedPreferences.getInstance();
  final deviceInfoStringifiedJson = prefs.getString("deviceInfo");
  final deviceInfoJson = jsonDecode(deviceInfoStringifiedJson!);
  return DeviceInfo.fromJson(deviceInfoJson!);
}






  static Future <DeviceInfo> _readAndroidDeviceInfo(AndroidDeviceInfo androidBuild) async{
    debugPrint("DeviceInfoService: DEVICE INFO: $androidBuild");
    String? uniqueDeviceId = await _getUniqueDeviceId();
    return DeviceInfo(
      marqueTelephone: androidBuild.brand, 
      modelTelephone: androidBuild.model, 
      imeiTelephone: uniqueDeviceId!, 
      numeroSerie: androidBuild.id
      );

  }



  static Future<DeviceInfo> _readIosDeviceInfo(IosDeviceInfo iosBuild) async{
    debugPrint("DeviceInfoService: DEVICE INFO: $iosBuild");
    String? uniqueDeviceId = await _getUniqueDeviceId();

    return DeviceInfo(
      marqueTelephone: iosBuild.name, 
      modelTelephone: iosBuild.model, 
      imeiTelephone: uniqueDeviceId!, 
      numeroSerie: iosBuild.identifierForVendor!
      );

  }


  static Future<String?> _getUniqueDeviceId() async{
    return await _mobileDeviceIdentifer.getDeviceId();
  }



}

