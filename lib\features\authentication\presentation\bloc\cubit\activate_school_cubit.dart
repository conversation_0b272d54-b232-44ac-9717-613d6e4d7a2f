import 'package:Kairos/core/error/failures.dart';
import 'package:Kairos/features/authentication/domain/entities/activation_request_entity.dart';
import 'package:Kairos/features/authentication/presentation/bloc/state/activate_school_state.dart';
import 'package:Kairos/features/schools/domain/usecases/get_user_profile_models_usecase.dart';
import 'package:Kairos/features/schools/data/datasources/schools_local_datasource.dart';
import 'package:Kairos/features/schools/data/models/user_profile_model.dart';
import 'package:Kairos/features/authentication/data/datasources/auth_local_datasource.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';



// Define Cubit
class ActivateSchoolCubit extends Cubit<ActivateSchoolState> {
  final GetUserProfileModelsUseCase _getUserProfileModelsUseCase;
  final SchoolsLocalDataSource _schoolsLocalDataSource;
  final AuthLocalDataSource _authLocalDataSource;

  ActivateSchoolCubit(
    this._getUserProfileModelsUseCase,
    this._schoolsLocalDataSource,
    this._authLocalDataSource,
  ):super(ActivateSchoolInitial()); // Initialize super with ActivateSchoolInitial state

  /// Validate authentication and activate school with the provided credentials.
  /// This method calls the getUserSchools endpoint to validate authentication.
  Future<void> activateSchool(ActivationRequestEntity requestEntity) async {
    emit(const ActivateSchoolLoading()); // Indicate loading state

    try {
      // Get phone number from local storage
      final phoneNumber = await _getPhoneNumber();
      if (phoneNumber == null) {
        emit(const ActivateSchoolError('Numéro de téléphone non trouvé. Veuillez vous reconnecter.'));
        return;
      }

      // Call getUserProfileModels to get the list of UserProfileModel objects
      final failureOrUserSchools = await _getUserProfileModelsUseCase(phoneNumber);

      failureOrUserSchools.fold(
        (failure) {
          // Handle failure and emit error state
          debugPrint('Failed to get user schools: ${_mapFailureToMessage(failure)}');
          emit(ActivateSchoolError(_mapFailureToMessage(failure)));
        },
        (userSchools) async {
          // Find matching user profile based on codeUtilisateur and codeEtab
          final matchingProfile = _findMatchingUserProfile(
            userSchools,
            requestEntity.username,
            requestEntity.codeEtab
          );

          if (matchingProfile == null) {
            emit(const ActivateSchoolError('Aucun profil correspondant trouvé pour ces identifiants.'));
            return;
          }

          // Check authentication status
          if (matchingProfile.isAuthenticated != true) {
            emit(const ActivateSchoolError('Vous n\'êtes pas autorisé à utiliser cet environnement.'));
            return;
          }

          // Store data in SharedPreferences
          await _storeUserData(matchingProfile, userSchools);

          // Emit success state
          emit(const ActivateSchoolSuccess());
        },
      );
    } catch (e) {
      debugPrint('Unexpected error during school activation: $e');
      emit(ActivateSchoolError('Une erreur inattendue s\'est produite: $e'));
    }
  }

  /// Get phone number from local storage
  Future<String?> _getPhoneNumber() async {
    try {
      return await _authLocalDataSource.getPhoneNumber();
    } catch (e) {
      debugPrint('Error getting phone number: $e');
      return null;
    }
  }

  /// Find matching user profile based on codeUtilisateur and codeEtab
  UserProfileModel? _findMatchingUserProfile(
    List<UserProfileModel> userProfiles,
    String codeUtilisateur,
    String codeEtab,
  ) {
    try {
      return userProfiles.firstWhere(
        (profile) =>
          profile.codeUtilisateur == codeUtilisateur &&
          profile.etablissement.codeEtab == codeEtab,
      );
    } catch (e) {
      debugPrint('No matching profile found: $e');
      return null;
    }
  }

  /// Store user data in SharedPreferences
  Future<void> _storeUserData(
    UserProfileModel matchingProfile,
    List<UserProfileModel> allUserSchools,
  ) async {
    try {
      // Store the complete matching UserProfileModel
      await _schoolsLocalDataSource.storeUserProfile(matchingProfile);

      // Store the nested utilisateur object
      await _schoolsLocalDataSource.storeUser(matchingProfile.utilisateur);

      // Store the nested etablissement object
      await _schoolsLocalDataSource.storeSchool(matchingProfile.etablissement);

      // Store the list of all user schools for liste_etablissement page
      await _schoolsLocalDataSource.storeUserSchools(allUserSchools);

      debugPrint('User data stored successfully');
    } catch (e) {
      debugPrint('Error storing user data: $e');
      throw Exception('Erreur lors de la sauvegarde des données utilisateur');
    }
  }

  String _mapFailureToMessage(Failure failure) {
    // Implement logic to map different failure types to user-friendly messages
    switch (failure.runtimeType) {
      case ServerFailure _:
        return 'Erreur serveur: Veuillez réessayer plus tard.';
      case NetworkFailure _:
        return 'Erreur réseau: Vérifiez votre connexion internet.';
      case AuthenticationFailure _:
        return 'Erreur d\'authentification: Vérifiez vos identifiants.';
      default:
        return 'Une erreur inattendue s\'est produite.';
    }
  }
}
