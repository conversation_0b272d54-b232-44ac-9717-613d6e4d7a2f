import 'package:Kairos/core/error/failures.dart';
import 'package:Kairos/core/services/device_info_service.dart';
import 'package:Kairos/features/authentication/domain/entities/activation_request_entity.dart';
import 'package:Kairos/features/authentication/domain/usecases/activate_school_usecase.dart';
import 'package:Kairos/features/authentication/presentation/bloc/state/activate_school_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart'; // Import Either



// Define Cubit
class ActivateSchoolCubit extends Cubit<ActivateSchoolState> {
  final ActivateSchoolUseCase _activateSchoolUsecase;
  /// Service for retrieving device information.
  final DeviceInfoService _deviceInfoService;

  ActivateSchoolCubit(
    this._activateSchoolUsecase,
    this._deviceInfoService,
  ):super(ActivateSchoolInitial()); // Initialize super with ActivateSchoolInitial state

  /// Activate a school with the provided credentials.
  /// This method makes a POST request to the activateSchool endpoint.
  Future<void> activateSchool(ActivationRequestEntity requestEntity) async {
    emit(const ActivateSchoolLoading()); // Indicate loading state

    // Convert ActivationRequestEntity to ActivationRequest model using the toModel method
    final request = requestEntity.toModel(_deviceInfoService);

    // _activateSchoolUseCase expects ActivationRequest
    final failureOrSuccess = await _activateSchoolUsecase(request);

    failureOrSuccess.fold(
      (failure) {
        // Handle failure and emit error state
        debugPrint('School activation failed: ${_mapFailureToMessage(failure)}');
        emit(ActivateSchoolError(_mapFailureToMessage(failure)));
      },
      (_) {
        // Handle success and emit success state
        debugPrint('School activated successfully.');
        // TODO: Define and emit a specific success state for school activation
      },
    );
  }

  String _mapFailureToMessage(Failure failure) {
    // Implement logic to map different failure types to user-friendly messages
    // This is a placeholder, you'll need to define specific failure types and messages
    switch (failure.runtimeType) {
      case ServerFailure:
        return 'Server Error: Please try again later.';
      case CacheFailure:
        return 'Cache Error: Data not available.';
      // Add more cases for specific failure types if needed
      default:
        return 'An unexpected error occurred.';
    }
  }
}
