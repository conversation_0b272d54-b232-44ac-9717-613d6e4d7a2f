import 'dart:async';

import 'package:Kairos/core/error/failures.dart';
import 'package:Kairos/features/schools/data/models/activation_request.dart';
import 'package:dartz/dartz.dart';

/// Abstract repository for school activation operations.
///
/// Defines the contract for activating a school using an activation request.
abstract class ActivateSchoolRepository {
  /// Activates a school using the provided [request].
  ///
  /// Returns a [Future] that completes with the result of the activation.
  /// The return type is dynamic as the specific success/failure response
  /// structure is not yet defined.
 
  /// Activate a school with the provided credentials.
  Future<Either<Failure, void>> activateSchool(ActivationRequest request);
}