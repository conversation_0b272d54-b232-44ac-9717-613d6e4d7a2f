import 'package:Kairos/features/authentication/domain/repositories/activate_school_repository.dart';
import 'package:Kairos/features/schools/data/models/activation_request.dart';
import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';

/// Use case for activating a school.
class ActivateSchoolUseCase implements UseCase<void, ActivationRequest> {
  final ActivateSchoolRepository repository;

  ActivateSchoolUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(ActivationRequest params) async {
    // Call the repository method to activate the school
    return await repository.activateSchool(params);
  }
}