




import 'package:Kairos/core/api/api_client.dart';
import 'package:Kairos/core/api/api_endpoints.dart';
import 'package:Kairos/core/api/api_exception.dart';
import 'package:Kairos/core/error/exceptions.dart';
import 'package:Kairos/features/schools/data/models/activation_request.dart';
import 'package:dio/dio.dart';

abstract class ActivateSchoolRemoteDatasource {

  /// Activate a school with the provided credentials.
  Future<void> activateSchool(ActivationRequest request);
}


class ActivateSchoolRemoteDatasourceImpl implements ActivateSchoolRemoteDatasource {
  final ApiClient apiClient;

  ActivateSchoolRemoteDatasourceImpl({required this.apiClient});

  @override
  Future<void> activateSchool(ActivationRequest request) async {
    try {
      // Make HTTP POST request to the activateSchool endpoint with the request payload
      final response = await apiClient.postWithToken(
        ApiEndpoints.activateSchool,
        data: request.toJson(),
      );

      // TODO: Handle successful activation response if needed (e.g., parse response data)
      // For now, assuming a successful response (status code 200) is enough
      if (response.statusCode != 200) {
         throw ServerException('Failed to activate school with status code: ${response.statusCode}');
      }

    } on DioException catch (e) {
      if (e.response != null) {
        // Handle API error response
        final apiException = ApiException.fromJson(
          e.response!.data,
          e.response!.statusCode,
        );
        throw ServerException(apiException.getUserMessage());
      } else {
        // Handle network error
        throw NetworkException('Erreur de connexion lors de l\'activation de l\'école: ${e.message}');
      }
    } catch (e) {
      if (e is ServerException || e is NetworkException) {
        rethrow;
      }
      throw ServerException('Erreur inattendue lors de l\'activation de l\'école: $e');
    }
  }
  
  
  }