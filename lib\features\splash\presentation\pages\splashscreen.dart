
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class Splashscreen extends StatefulWidget{
  const Splashscreen({super.key});

  @override
  State<Splashscreen> createState() => _SplashscreenState();
}

class _SplashscreenState extends State<Splashscreen> with TickerProviderStateMixin{

  late final AnimationController _animationController;
  bool _didAnimationLoad = false;


  @override
  void initState(){
    super.initState();
    _animationController = AnimationController(vsync: this);

    _animationController.addStatusListener((status){
      if(status == AnimationStatus.completed){
        setState((){
          _didAnimationLoad = true;
          debugPrint("animation completed: $_didAnimationLoad");
          _navigateToAccueilPage();
        });
      }
    });
  }


  @override
  void dispose(){
    super.dispose();
    _animationController.dispose();
  }


  void _navigateToAccueilPage(){
    Navigator.of(context).pushReplacementNamed("/accueil");
  }


  @override
  Widget build(BuildContext context){
    return Scaffold(
      body: Center(
        child: Lottie.asset("assets/animations/kairos_lottie.json",
        controller: _animationController,
        onLoaded: (composition){
          _animationController.duration = composition.duration;
          _animationController.forward();
        },
        width: MediaQuery.of(context).size.width * .80,
        fit: BoxFit.contain
        ),
      ),
    );
  }
}