import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ReactivateDialog extends StatelessWidget {
  final VoidCallback? onReactivate;
  final VoidCallback? onCancel;

  const ReactivateDialog({super.key, this.onReactivate, this.onCancel});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      title: const Text(
        "RÉACTIVER LE COMPTE",
        textAlign: TextAlign.center,
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            'assets/icons/icone_acces_refuse.svg',
            width: 50,
            height: 50,
            colorFilter: ColorFilter.mode(Colors.red, BlendMode.srcIn),
          ),
          const SizedBox(height: 16),
          const Text(
            "Cet utilisateur à déjà un espace, voulez vous le réactiver?",
            textAlign: TextAlign.center,
          ),
        ],
      ),
      actionsAlignment: MainAxisAlignment.center,
      actionsPadding: const EdgeInsets.all(15.0),
      actionsOverflowAlignment: OverflowBarAlignment.center,
      actions: [
        FilledButton(
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Theme.of(context).colorScheme.secondary),
            minimumSize: WidgetStateProperty.all(const Size(140, 40)),
          ),
          onPressed: () {
            Navigator.of(context).pop();
            onCancel?.call();
          },
          child: const Text('NON'),
        ),
        FilledButton(
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
            minimumSize: WidgetStateProperty.all(const Size(140, 40)),
          ),
          onPressed: () {
            Navigator.of(context).pop();
            onReactivate?.call(); // Call the provided callback
          },
          child: const Text('OUI'),
        ),
      ],
    );
  }
}
