import 'package:Kairos/features/authentication/data/datasources/activate_school_remote_datasource.dart';
import 'package:Kairos/features/authentication/data/repositories/activate_school_repository_impl.dart';
import 'package:Kairos/features/authentication/domain/repositories/activate_school_repository.dart';
import 'package:Kairos/features/authentication/domain/usecases/verify_pin_usecase.dart';
import 'package:Kairos/features/authentication/presentation/bloc/cubit/code_activation_cubit.dart';
import 'package:Kairos/features/authentication/domain/usecases/activate_school_usecase.dart';
import 'package:get_it/get_it.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../api/api_client.dart';
import '../network/network_info.dart';
import '../services/device_info_service.dart';

// Authentication feature imports
import '../../features/authentication/data/datasources/auth_local_datasource.dart';
import '../../features/authentication/data/datasources/auth_remote_datasource.dart';
import '../../features/authentication/data/repositories/auth_repository_impl.dart';
import '../../features/authentication/domain/repositories/auth_repository.dart';
import '../../features/authentication/domain/usecases/send_sms_usecase.dart';
import '../../features/authentication/domain/usecases/resend_sms_usecase.dart';
import '../../features/authentication/domain/usecases/check_response_usecase.dart';
import '../../features/authentication/presentation/bloc/cubit/auth_cubit.dart';
import '../../features/authentication/presentation/bloc/cubit/phone_authentication_cubit.dart';

// Schools feature imports
import '../../features/schools/data/datasources/schools_remote_datasource.dart';
import '../../features/schools/data/repositories/schools_repository_impl.dart';
import '../../features/schools/domain/repositories/schools_repository.dart';
import '../../features/schools/domain/usecases/get_schools_usecase.dart';
import '../../features/schools/presentation/bloc/schools_cubit.dart';
import '../../features/schools/domain/usecases/get_user_schools_usecase.dart'; // Import GetUserSchoolsUseCase

/// Service locator instance
final sl = GetIt.instance;

/// Initialize all dependencies
Future<void> init() async {
  //! Features - Authentication
  // Data sources
  sl.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(apiClient: sl()),
  );
  sl.registerLazySingleton<AuthLocalDataSource>(
    () => AuthLocalDataSourceImpl(sharedPreferences: sl()),
  );
  sl.registerLazySingleton<ActivateSchoolRemoteDatasource>(
    () => ActivateSchoolRemoteDatasourceImpl(apiClient: sl()),
  );

  // Repository
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
    ),
  );
  sl.registerLazySingleton<ActivateSchoolRepository>(
    () => ActivateSchoolRepositoryImpl(remoteDataSource: sl()),
  );
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      remoteDataSource: sl(),
      localDataSource: sl(),
    ),
  );

  // Use cases
  sl.registerLazySingleton(() => SendSmsUseCase(sl()));
  sl.registerLazySingleton(() => VerifyPinUseCase(sl()));
  sl.registerLazySingleton(() => ResendSmsUseCase(sl()));
  sl.registerLazySingleton(() => CheckResponseUseCase(sl())); // Register the new use case

  // BLoC/Cubit
  sl.registerFactory(() => AuthCubit());
  sl.registerFactory(() => CodeActivationCubit(verifyPinUseCase: sl()));
  sl.registerFactory(() => PhoneAuthenticationCubit(sendSmsUseCase: sl(), 
                                                    resendSmsUseCase: sl(), 
                                                    checkResponseUseCase: sl())); // Provide the new use case
  
  //! Features - Dashboard
  // TODO: Register dashboard dependencies
  
  //! Features - Finances
  // TODO: Register finances dependencies
  
  //! Features - Grades
  // TODO: Register grades dependencies
  
  //! Features - Schedule
  // TODO: Register schedule dependencies
  
  //! Features - Absences
  // TODO: Register absences dependencies
  
  //! Features - Course Log
  // TODO: Register course log dependencies
  
  //! Features - Student Records
  // TODO: Register student records dependencies
  
  //! Features - Profile
  // TODO: Register profile dependencies
  
  //! Features - Notifications
  // TODO: Register notifications dependencies
  
  //! Features - Schools
  // Data sources
  sl.registerLazySingleton<SchoolsRemoteDataSource>(
    () => SchoolsRemoteDataSourceImpl(apiClient: sl()),
  );

  // Repository
  sl.registerLazySingleton<SchoolsRepository>(
    () => SchoolsRepositoryImpl(remoteDataSource: sl()),
  );

  // Use cases
  sl.registerLazySingleton(() => GetSchoolsUseCase(sl()));
  sl.registerLazySingleton(() => GetUserSchoolsUseCase(sl())); // Register GetUserSchoolsUseCase
  sl.registerLazySingleton(() => ActivateSchoolUseCase(sl())); // Register ActivateSchoolUseCase

  // BLoC/Cubit
  sl.registerFactory(() => SchoolsCubit(sl(), sl())); // Provide use cases, ApiClient, and ActivateSchoolUseCase

  //! Features - Splash
  // TODO: Register splash dependencies
  
  //! Core
  sl.registerLazySingleton<ApiClient>(() => ApiClient(authLocalDataSource: sl()));
  sl.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl(sl()));
  sl.registerLazySingleton(() => DeviceInfoService());
  
  //! External
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerLazySingleton(() => sharedPreferences);
  sl.registerLazySingleton(() => Connectivity());
}

/// Register feature-specific dependencies
/// This method will be called for each feature as they are implemented
void registerFeatureDependencies() {
  // TODO: Implement feature-specific dependency registration
}
