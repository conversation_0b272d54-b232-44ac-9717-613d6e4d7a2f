import 'package:Kairos/core/widgets/common/snackbar_widget.dart';
import 'package:Kairos/core/widgets/layout/custom_app_bar.widget.dart';
import 'package:Kairos/features/authentication/domain/entities/activation_request_entity.dart';
import 'package:Kairos/features/authentication/presentation/bloc/state/activate_school_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:Kairos/features/schools/domain/entities/etablissement_entity.dart';
import 'package:Kairos/features/schools/presentation/pages/widgets/school_selection_modal.widget.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:Kairos/core/enums/header_enums.dart';
import 'package:Kairos/core/di/injection_container.dart'; // Import sl<T> for dependency injection
import 'package:Kairos/core/services/device_info_service.dart';
import 'package:Kairos/features/authentication/presentation/bloc/cubit/activate_school_cubit.dart';
import 'package:Kairos/features/schools/presentation/bloc/schools_cubit.dart'; // Keep SchoolsCubit import for the modal

class ActivateSchoolPage extends StatefulWidget {
  const ActivateSchoolPage({super.key});

  @override
  State<ActivateSchoolPage> createState() => _ActivateSchoolPageState();
}

class _ActivateSchoolPageState extends State<ActivateSchoolPage> {
  // State variable to hold the selected school
  EtablissementEntity? _selectedSchool;

  // Text editing controllers for input fields
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  @override
  void dispose() {
    // Dispose controllers when the widget is removed
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }


// ===================================== _showSchoolSelectionModal =================================
   // Method to show the school selection bottom modal
  void _showSchoolSelectionModal() async {
    // Load schools data before showing the modal
    // Note: This still uses SchoolsCubit to get the list of schools for the modal
    context.read<SchoolsCubit>().getSchools();

    final EtablissementEntity? selectedSchool = await showModalBottomSheet<EtablissementEntity?>(
      context: context,
      builder: (BuildContext context) {
        // Use the SchoolSelectionModal widget
        return SchoolSelectionModal();
      },
    );

    if (selectedSchool != null) {
      setState(() {
        _selectedSchool = selectedSchool;
      });
    }
  }




// ===================================== build =================================
  @override
  Widget build(BuildContext context) {
    return BlocProvider<ActivateSchoolCubit>(
      create: (context) => sl<ActivateSchoolCubit>(), // Provide ActivateSchoolCubit using GetIt
      child: BlocConsumer<ActivateSchoolCubit, ActivateSchoolState>(
        listener: (context, state) {
          if (state is ActivateSchoolSuccess) {
            // Navigate to the list of establishments page on success
            // TODO: Verify the correct route name for liste_etablissement.page
            Navigator.pushReplacementNamed(context, '/liste_etablissement');
          } else if (state is ActivateSchoolError) {
            // Show an error message on error
            CustomSnackbar(message: state.errorMessage).getSnackBar();
          }
        },
        builder: (context, state) {
          // Determine loading state from Cubit state
          final bool isLoading = state is ActivateSchoolLoading;

          return Scaffold(
            body: CustomScrollView(
              slivers: [
                CustomAppBar(
                  pageSection: HeaderEnum.dashboard, // Using dashboard as a placeholder
                  title: "", // Empty title as requested
                  isSearchBarVisible: true,
                ),
                SliverToBoxAdapter( // Wrap the rest of the body in SliverToBoxAdapter
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      SizedBox(height: 20),
                      Text("ACTIVATION D'UN NOUVEL ÉTABLISSEMENT", style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor)),
                      Divider(color: Theme.of(context).primaryColor, thickness: 2, height: 20, indent: 100, endIndent: 100,),
                      Text(
                        "Nouvel établissement",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        "Pour activer un nouvel établissement, veuillez choisir l'établissement, votre identifiant ainsi que votre mot de passe",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      SizedBox(height: 20),
                      SizedBox(height: 20), // Adjusted spacing below AppBar

                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Column(
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "ETABLISSEMENT",
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.lightBlueAccent,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: 8),
                                InkWell(
                                  onTap: _showSchoolSelectionModal,
                                  child: Container(
                                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 14),
                                    decoration: BoxDecoration(
                                      border: Border.all(color: Colors.grey.shade400),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          _selectedSchool?.libelleEtab ?? "Ecole",
                                          style: TextStyle(
                                            fontSize: 16,
                                            color: _selectedSchool == null ? Colors.grey.shade600 : Colors.black,
                                          ),
                                        ),
                                        Icon(Icons.arrow_drop_down, color: Colors.black),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 20),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "VOTRE IDENTIFIANT",
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.lightBlueAccent,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: 8),
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 0), // Padding adjusted for TextFormField
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey.shade400),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: TextFormField(
                                    controller: _usernameController, // Assign controller
                                    decoration: InputDecoration(
                                      hintText: "ex: 2208900",
                                      hintStyle: TextStyle(color: Colors.grey.shade600),
                                      contentPadding: EdgeInsets.symmetric(horizontal: 0, vertical: 14), // Vertical padding moved here
                                      border: InputBorder.none, // Remove underline border
                                      enabledBorder: InputBorder.none, // Remove underline border
                                      focusedBorder: InputBorder.none, // Remove underline border
                                    ),
                                    keyboardType: TextInputType.number,
                                    validator: (value) {
                                      if (value!.isEmpty) {
                                        return "Veuillez saisir votre identifiant";
                                      } else {
                                        return null;
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 20),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "VOTRE MOT DE PASSE",
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.lightBlueAccent,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: 8),
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 0), // Padding adjusted for TextFormField
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey.shade400),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: TextFormField(
                                    controller: _passwordController, // Assign controller
                                    decoration: InputDecoration(
                                      hintText: "ex: ••••••••",
                                      hintStyle: TextStyle(color: Colors.grey.shade600),
                                      contentPadding: EdgeInsets.symmetric(horizontal: 0, vertical: 14), // Vertical padding moved here
                                      border: InputBorder.none, // Remove underline border
                                      enabledBorder: InputBorder.none, // Remove underline border
                                      focusedBorder: InputBorder.none, // Remove underline border
                                    ),
                                    obscureText: true,
                                    validator: (value) {
                                      if (value!.isEmpty) {
                                        return "Veuillez saisir votre mot de passe";
                                      } else {
                                        return null;
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 30),
                            FilledButton(
                              style: ButtonStyle(
                                backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                                minimumSize: WidgetStateProperty.all(Size(150, 60)),
                                shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                                  RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(25.0),
                                  ),
                                ),
                              ),
                              onPressed: isLoading ? null : () async { // Disable button when loading
                                if (_selectedSchool == null) {
                                  // Show an error if no school is selected
                                  CustomSnackbar(message: "Veuillez sélectionner un établissement").getSnackBar();
                                  return;
                                }

                                // Get values from input fields
                                final String username = _usernameController.text;
                                final String password = _passwordController.text;
                                final String codeEtab = _selectedSchool!.codeEtab;

                                // Call activateSchool method on ActivateSchoolCubit
                                context.read<ActivateSchoolCubit>().activateSchool(
                                  ActivationRequestEntity(
                                    codeEtab: codeEtab,
                                    username: username,
                                    password: password,
                                  ),
                                );
                              },
                              child: isLoading // Show spinner when loading
                                  ? const SizedBox(
                                      width: 24,
                                      height: 24,
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                        strokeWidth: 2,
                                      ),
                                    )
                                  : Text(
                                      "ACTIVER",
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ),
                            ),
                            SizedBox(height: 60),
                            SvgPicture.asset(
                              'assets/images/logo_footer.svg',
                              height: 30,
                            ),
                            SizedBox(height: 10),
                          ],
                        ),
                      ),]
                    ),
                ),
            ],
          ),
        );
      }
      
      ),
      
      
      );
     // Close BlocProvider
  }

 
}
