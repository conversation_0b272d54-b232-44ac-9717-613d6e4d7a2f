import 'package:Kairos/core/services/device_info_service.dart';
import 'package:Kairos/features/schools/data/models/activation_request.dart';

class ActivationRequestEntity {
  final String codeEtab;
  final String username;
  final String password;

  const ActivationRequestEntity({
    required this.codeEtab,
    required this.username,
    required this.password,
  });

  Map<String, dynamic> toJson() => {
        'codeEtab': codeEtab,
        'username': username,
        'password': password,
      };
  /// Converts the entity to an ActivationRequest model.
  /// Retrieves device information from the provided DeviceInfoService.
  ActivationRequest toModel(DeviceInfoService deviceInfoService) {
    // Get device info from the service
    final deviceInfo = DeviceInfoService.deviceInfo;

    return ActivationRequest(
      codeEtab: codeEtab,
      password: password,
      codeUtilisateur: username, // Map username to codeUtilisateur
      numeroTelephone: deviceInfo.numeroTelephone,
      marqueTelephone: deviceInfo.marqueTelephone,
      modelTelephone: deviceInfo.modelTelephone,
      imeiTelephone: deviceInfo.imeiTelephone,
      numeroSerie: deviceInfo.numeroSerie,
    );

  }
}
